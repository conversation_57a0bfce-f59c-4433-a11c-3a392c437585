<execution>
  <constraint>
    ## 技术选型的客观限制
    - **一人公司资源约束**：考虑学习成本、维护成本和部署复杂度
    - **项目规模约束**：技术选型必须匹配项目的实际规模和复杂度
    - **性能要求约束**：满足需求文档中明确的性能指标和响应时间
    - **环境兼容约束**：考虑目标部署环境的技术栈兼容性
    - **长期维护约束**：选择有长期支持和活跃社区的技术
  </constraint>

  <rule>
    ## 技术选型强制规则
    - **调研先行**：重要技术选型必须通过`research_mode_shrimp-video-factory`进行充分调研
    - **决策记录**：所有技术选型决策必须记录理由和权衡过程
    - **成熟度优先**：优先选择经过验证的成熟技术栈
    - **生态考量**：必须考虑技术的生态系统和社区支持
    - **退出策略**：为关键技术选型准备备选方案和迁移策略
  </rule>

  <guideline>
    ## 技术选型指导原则
    - **简单性原则**：在满足需求的前提下选择最简单的技术方案
    - **学习成本控制**：平衡技术先进性与学习维护成本
    - **标准化优先**：优先选择行业标准和主流技术
    - **组合优化**：考虑技术栈的整体协调性和兼容性
    - **未来适应性**：选择有良好发展前景的技术方向
  </guideline>

  <process>
    ## 技术选型标准流程
    
    ### 阶段1：技术需求分析
    ```bash
    # 1. 分析功能和性能需求
    analyze_task_shrimp-video-factory "技术需求分析：功能要求和性能指标"
    
    # 2. 识别技术约束条件
    process_thought_shrimp-video-factory "项目技术约束和限制条件分析"
    
    # 3. 确定技术选型范围
    process_thought_shrimp-video-factory "技术选型的边界和候选范围确定"
    ```
    
    #### 技术需求清单
    - **功能性需求**：技术必须支持的核心功能
    - **性能需求**：响应时间、吞吐量、并发数等指标
    - **可靠性需求**：可用性、容错性、数据一致性要求
    - **安全性需求**：认证、授权、数据保护等安全要求
    - **可维护性需求**：代码可读性、调试便利性、升级便利性
    
    ### 阶段2：候选技术调研
    ```bash
    # 1. 前端技术栈调研
    research_mode_shrimp-video-factory "前端技术栈选型：React vs Vue vs Angular 适用性分析"
    
    # 2. 后端技术栈调研
    research_mode_shrimp-video-factory "后端技术栈选型：Node.js vs Python vs Go 性能和维护性对比"
    
    # 3. 数据库技术调研
    research_mode_shrimp-video-factory "数据库选型：关系型 vs NoSQL 适用场景分析"
    
    # 4. 部署技术调研
    research_mode_shrimp-video-factory "部署方案选型：Docker vs 传统部署 复杂度和便利性对比"
    ```
    
    #### 技术调研维度
    
    **成熟度评估**
    - 版本稳定性和发布频率
    - 社区活跃度和贡献者数量
    - 企业采用案例和成功实践
    - 官方文档和教程完整性
    
    **适用性评估**
    - 功能匹配度和实现难易度
    - 性能表现和优化空间
    - 学习曲线和上手难度
    - 与现有技术栈的兼容性
    
    **维护性评估**
    - 代码可读性和调试便利性
    - 问题排查和解决的便利性
    - 升级和迁移的复杂度
    - 长期维护的人力成本
    
    ### 阶段3：技术选型决策
    ```bash
    # 1. 建立决策矩阵
    process_thought_shrimp-video-factory "技术选型决策矩阵构建和权重分配"
    
    # 2. 量化评估候选技术
    analyze_task_shrimp-video-factory "候选技术的多维度量化评估"
    
    # 3. 风险评估和应对策略
    process_thought_shrimp-video-factory "技术选型风险识别和缓解策略"
    ```
    
    #### 决策矩阵示例
    
    | 技术方案 | 功能匹配(30%) | 学习成本(25%) | 维护性(20%) | 性能(15%) | 生态(10%) | 总分 |
    |----------|---------------|---------------|-------------|-----------|-----------|------|
    | 方案A    | 4.5           | 4.0           | 4.5         | 4.0       | 4.5       | 4.3  |
    | 方案B    | 4.0           | 4.5           | 4.0         | 4.5       | 4.0       | 4.2  |
    | 方案C    | 3.5           | 3.5           | 3.5         | 4.5       | 3.5       | 3.6  |
    
    #### 技术选型决策记录
    ```markdown
    ## 技术选型决策记录
    
    ### 前端技术栈
    **选择**：React + TypeScript
    **理由**：
    - 成熟稳定，社区支持良好
    - TypeScript提供类型安全
    - 组件化开发提高可维护性
    - 学习资源丰富，问题解决便利
    
    ### 后端技术栈
    **选择**：Node.js + Express
    **理由**：
    - JavaScript全栈，降低学习成本
    - 异步处理能力强，适合I/O密集型应用
    - npm生态丰富，开发效率高
    - 部署简单，资源消耗适中
    
    ### 数据库选择
    **选择**：PostgreSQL
    **理由**：
    - 功能强大的关系型数据库
    - 支持JSON数据类型，兼顾灵活性
    - 性能优秀，扩展性好
    - 开源免费，社区活跃
    ```
    
    ### 阶段4：技术栈集成验证
    ```bash
    # 1. 技术栈兼容性验证
    analyze_task_shrimp-video-factory "技术栈整体兼容性和集成复杂度分析"
    
    # 2. 原型验证（如需要）
    execute_task_shrimp-video-factory "关键技术的原型验证和可行性测试"
    
    # 3. 最终方案确认
    reflect_task_shrimp-video-factory "技术选型方案的批判性审查和优化"
    ```
    
    #### 集成验证要点
    - **技术栈协调性**：各技术组件间的协调工作能力
    - **开发工具链**：开发、测试、部署工具链的完整性
    - **性能基准**：技术栈整体的性能表现基准
    - **部署复杂度**：整体技术栈的部署和运维复杂度
  </process>

  <criteria>
    ## 技术选型质量标准
    
    ### 调研充分性
    - ✅ 对候选技术进行了全面的调研分析
    - ✅ 调研覆盖了功能、性能、维护性等多个维度
    - ✅ 收集了足够的技术对比数据和案例
    - ✅ 分析了技术的发展趋势和长期前景
    
    ### 决策合理性
    - ✅ 技术选型与项目需求高度匹配
    - ✅ 充分考虑了一人公司的资源约束
    - ✅ 平衡了技术先进性与实用性
    - ✅ 技术选型决策有充分的理由支撑
    
    ### 风险控制
    - ✅ 识别了技术选型的主要风险点
    - ✅ 制定了风险缓解和应对策略
    - ✅ 为关键技术准备了备选方案
    - ✅ 评估了技术迁移的可行性和成本
    
    ### 实施可行性
    - ✅ 技术栈整体协调性良好
    - ✅ 开发工具链完整且易用
    - ✅ 部署和运维复杂度可控
    - ✅ 为开发阶段提供了清晰的技术指导
    
    ### 文档记录
    - ✅ 技术选型过程和决策完整记录
    - ✅ 技术调研结果详细文档化
    - ✅ 技术选型理由清晰明确
    - ✅ 为后续开发提供了技术参考
  </criteria>
</execution>
